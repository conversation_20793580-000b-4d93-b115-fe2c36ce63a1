# Script para diagnosticar e corrigir erro MSI 1603
# Autor: <PERSON> - NVirtual
# Versão: 1.0

param(
    [Parameter(Mandatory=$false)]
    [switch]$Fix
)

# Função para log
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch($Level) {
        "ERROR" { "Red" }
        "WARNING" { "Yellow" }
        "SUCCESS" { "Green" }
        default { "White" }
    }
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

Write-Log "=== DIAGNÓSTICO E CORREÇÃO DO ERRO MSI 1603 ===" -Level "SUCCESS"

# 1. Verificar privilégios de administrador
Write-Log "1. Verificando privilégios de administrador..."
$currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
$principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
$isAdmin = $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if ($isAdmin) {
    Write-Log "✅ Executando como Administrador" -Level "SUCCESS"
} else {
    Write-Log "❌ NÃO está executando como Administrador" -Level "ERROR"
    Write-Log "SOLUÇÃO: Execute o PowerShell como Administrador" -Level "WARNING"
}

# 2. Verificar serviços do Zabbix
Write-Log "2. Verificando serviços do Zabbix existentes..."
$zabbixServices = Get-Service | Where-Object { $_.Name -like "*Zabbix*" }
if ($zabbixServices) {
    Write-Log "❌ Encontrados serviços do Zabbix:" -Level "WARNING"
    foreach ($service in $zabbixServices) {
        Write-Log "  - $($service.Name): $($service.Status)" -Level "WARNING"
    }
    if ($Fix) {
        Write-Log "Parando e removendo serviços..." -Level "INFO"
        foreach ($service in $zabbixServices) {
            try {
                Stop-Service -Name $service.Name -Force -ErrorAction SilentlyContinue
                & sc.exe delete $service.Name 2>&1 | Out-Null
                Write-Log "Serviço $($service.Name) removido" -Level "SUCCESS"
            }
            catch {
                Write-Log "Erro ao remover $($service.Name): $($_.Exception.Message)" -Level "WARNING"
            }
        }
    }
} else {
    Write-Log "✅ Nenhum serviço do Zabbix encontrado" -Level "SUCCESS"
}

# 3. Verificar processos do Zabbix
Write-Log "3. Verificando processos do Zabbix..."
$zabbixProcesses = Get-Process | Where-Object { $_.ProcessName -like "*zabbix*" }
if ($zabbixProcesses) {
    Write-Log "❌ Encontrados processos do Zabbix rodando:" -Level "WARNING"
    foreach ($proc in $zabbixProcesses) {
        Write-Log "  - $($proc.ProcessName) (PID: $($proc.Id))" -Level "WARNING"
    }
    if ($Fix) {
        Write-Log "Terminando processos..." -Level "INFO"
        foreach ($proc in $zabbixProcesses) {
            try {
                Stop-Process -Id $proc.Id -Force
                Write-Log "Processo $($proc.ProcessName) terminado" -Level "SUCCESS"
            }
            catch {
                Write-Log "Erro ao terminar $($proc.ProcessName): $($_.Exception.Message)" -Level "WARNING"
            }
        }
    }
} else {
    Write-Log "✅ Nenhum processo do Zabbix rodando" -Level "SUCCESS"
}

# 4. Verificar instalações MSI existentes
Write-Log "4. Verificando instalações MSI do Zabbix..."
try {
    $zabbixProducts = Get-WmiObject -Class Win32_Product -Filter "Name LIKE '%Zabbix%'" -ErrorAction SilentlyContinue
    if ($zabbixProducts) {
        Write-Log "❌ Encontradas instalações MSI do Zabbix:" -Level "WARNING"
        foreach ($product in $zabbixProducts) {
            Write-Log "  - $($product.Name) (Versão: $($product.Version))" -Level "WARNING"
        }
        if ($Fix) {
            Write-Log "Removendo instalações MSI..." -Level "INFO"
            foreach ($product in $zabbixProducts) {
                try {
                    $result = $product.Uninstall()
                    if ($result.ReturnValue -eq 0) {
                        Write-Log "Produto $($product.Name) removido" -Level "SUCCESS"
                    } else {
                        Write-Log "Erro ao remover $($product.Name): Código $($result.ReturnValue)" -Level "WARNING"
                    }
                }
                catch {
                    Write-Log "Erro ao remover $($product.Name): $($_.Exception.Message)" -Level "WARNING"
                }
            }
        }
    } else {
        Write-Log "✅ Nenhuma instalação MSI do Zabbix encontrada" -Level "SUCCESS"
    }
}
catch {
    Write-Log "Erro ao consultar produtos MSI: $($_.Exception.Message)" -Level "WARNING"
}

# 5. Verificar diretórios do Zabbix
Write-Log "5. Verificando diretórios do Zabbix..."
$zabbixDirs = @(
    "C:\Program Files\Zabbix Agent",
    "C:\Program Files\Zabbix Agent 2",
    "C:\Program Files (x86)\Zabbix Agent",
    "C:\Program Files (x86)\Zabbix Agent 2"
)

$foundDirs = @()
foreach ($dir in $zabbixDirs) {
    if (Test-Path $dir) {
        $foundDirs += $dir
        Write-Log "❌ Diretório encontrado: $dir" -Level "WARNING"
    }
}

if ($foundDirs.Count -eq 0) {
    Write-Log "✅ Nenhum diretório do Zabbix encontrado" -Level "SUCCESS"
} elseif ($Fix) {
    Write-Log "Removendo diretórios..." -Level "INFO"
    foreach ($dir in $foundDirs) {
        try {
            Remove-Item -Path $dir -Recurse -Force -ErrorAction Stop
            Write-Log "Diretório $dir removido" -Level "SUCCESS"
        }
        catch {
            Write-Log "Erro ao remover $dir : $($_.Exception.Message)" -Level "WARNING"
        }
    }
}

# 6. Verificar Windows Installer Service
Write-Log "6. Verificando Windows Installer Service..."
$msiService = Get-Service -Name "msiserver" -ErrorAction SilentlyContinue
if ($msiService) {
    Write-Log "Status do Windows Installer: $($msiService.Status)" -Level "INFO"
    if ($msiService.Status -ne "Running") {
        Write-Log "❌ Windows Installer não está rodando" -Level "WARNING"
        if ($Fix) {
            try {
                Start-Service -Name "msiserver"
                Write-Log "Windows Installer iniciado" -Level "SUCCESS"
            }
            catch {
                Write-Log "Erro ao iniciar Windows Installer: $($_.Exception.Message)" -Level "ERROR"
            }
        }
    } else {
        Write-Log "✅ Windows Installer está rodando" -Level "SUCCESS"
    }
} else {
    Write-Log "❌ Windows Installer Service não encontrado" -Level "ERROR"
}

# 7. Verificar espaço em disco
Write-Log "7. Verificando espaço em disco..."
$drives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 }
foreach ($drive in $drives) {
    $freeGB = [math]::Round($drive.FreeSpace / 1GB, 2)
    $totalGB = [math]::Round($drive.Size / 1GB, 2)
    $percentFree = [math]::Round(($drive.FreeSpace / $drive.Size) * 100, 1)
    
    if ($freeGB -lt 1) {
        Write-Log "❌ Drive $($drive.DeviceID) com pouco espaço: $freeGB GB livre de $totalGB GB ($percentFree%)" -Level "ERROR"
    } elseif ($freeGB -lt 5) {
        Write-Log "⚠️ Drive $($drive.DeviceID) com espaço limitado: $freeGB GB livre de $totalGB GB ($percentFree%)" -Level "WARNING"
    } else {
        Write-Log "✅ Drive $($drive.DeviceID): $freeGB GB livre de $totalGB GB ($percentFree%)" -Level "SUCCESS"
    }
}

# 8. Verificar arquivos temporários
Write-Log "8. Verificando arquivos temporários..."
$tempFiles = Get-ChildItem -Path $env:TEMP -Filter "*zabbix*" -ErrorAction SilentlyContinue
if ($tempFiles) {
    Write-Log "❌ Encontrados arquivos temporários do Zabbix:" -Level "WARNING"
    foreach ($file in $tempFiles) {
        Write-Log "  - $($file.FullName)" -Level "WARNING"
    }
    if ($Fix) {
        Write-Log "Removendo arquivos temporários..." -Level "INFO"
        foreach ($file in $tempFiles) {
            try {
                Remove-Item -Path $file.FullName -Force
                Write-Log "Arquivo $($file.Name) removido" -Level "SUCCESS"
            }
            catch {
                Write-Log "Erro ao remover $($file.Name): $($_.Exception.Message)" -Level "WARNING"
            }
        }
    }
} else {
    Write-Log "✅ Nenhum arquivo temporário do Zabbix encontrado" -Level "SUCCESS"
}

# 9. Verificar registro do Windows
Write-Log "9. Verificando entradas do registro..."
$regPaths = @(
    "HKLM:\SOFTWARE\Zabbix",
    "HKLM:\SOFTWARE\WOW6432Node\Zabbix"
)

$foundRegKeys = @()
foreach ($regPath in $regPaths) {
    if (Test-Path $regPath) {
        $foundRegKeys += $regPath
        Write-Log "❌ Entrada do registro encontrada: $regPath" -Level "WARNING"
    }
}

if ($foundRegKeys.Count -eq 0) {
    Write-Log "✅ Nenhuma entrada do registro do Zabbix encontrada" -Level "SUCCESS"
} elseif ($Fix) {
    Write-Log "Removendo entradas do registro..." -Level "INFO"
    foreach ($regPath in $foundRegKeys) {
        try {
            Remove-Item -Path $regPath -Recurse -Force
            Write-Log "Entrada $regPath removida" -Level "SUCCESS"
        }
        catch {
            Write-Log "Erro ao remover $regPath : $($_.Exception.Message)" -Level "WARNING"
        }
    }
}

# 10. Resumo e recomendações
Write-Log "=== RESUMO DO DIAGNÓSTICO ===" -Level "SUCCESS"

$issues = 0
if (-not $isAdmin) { $issues++ }
if ($zabbixServices) { $issues++ }
if ($zabbixProcesses) { $issues++ }
if ($zabbixProducts) { $issues++ }
if ($foundDirs.Count -gt 0) { $issues++ }

if ($issues -eq 0) {
    Write-Log "✅ Sistema limpo - pronto para instalação" -Level "SUCCESS"
} else {
    Write-Log "❌ Encontrados $issues problemas que podem causar erro 1603" -Level "WARNING"
    
    if (-not $Fix) {
        Write-Log "PARA CORRIGIR AUTOMATICAMENTE:" -Level "INFO"
        Write-Log "Execute: .\Fix-MSI-Error-1603.ps1 -Fix" -Level "INFO"
    }
}

Write-Log "PRÓXIMOS PASSOS:" -Level "INFO"
Write-Log "1. Corrija os problemas identificados" -Level "INFO"
Write-Log "2. Execute o script de instalação com -Force:" -Level "INFO"
Write-Log "   -ZabbixServerIP '*************' -AgentHostname 'SEU-HOST' -Force" -Level "INFO"
Write-Log "3. Se ainda falhar, tente instalação offline com -LocalMSIPath" -Level "INFO"

Write-Log "Diagnóstico concluído!" -Level "SUCCESS"
