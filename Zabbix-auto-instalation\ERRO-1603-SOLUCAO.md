# 🚨 Solução para Erro MSI 1603 - Zabbix Agent 2

## ❌ Problema
```
[ERROR] Erro na instalação: MSI retornou código de erro: 1603
[ERROR] ERRO DURANTE A INSTALAÇÃO: MSI retornou código de erro: 1603
```

## 🔍 O que é o erro 1603?
O erro 1603 é um **erro fatal do Windows Installer** que indica que a instalação falhou. As causas mais comuns são:

- ✅ **Instalação anterior corrompida**
- ✅ **Serviços do Zabbix ainda rodando**
- ✅ **Processos do Zabbix em execução**
- ✅ **Conflitos de arquivos/diretórios**
- ✅ **Permissões insuficientes**
- ✅ **Problemas no Windows Installer**

## 🛠️ Soluções (Execute em ordem)

### **Solução 1: Diagnóstico Automático** ⭐ (RECOMENDADA)
```powershell
# 1. Execute o script de diagnóstico
.\Fix-MSI-Error-1603.ps1

# 2. Se encontrar problemas, execute a correção automática
.\Fix-MSI-Error-1603.ps1 -Fix

# 3. Tente a instalação novamente com -Force
-ZabbixServerIP "*************" -AgentHostname "SEU-HOSTNAME" -Force
```

### **Solução 2: Limpeza Manual**
```powershell
# 1. Parar todos os serviços do Zabbix
Get-Service | Where-Object { $_.Name -like "*Zabbix*" } | Stop-Service -Force

# 2. Remover serviços
sc.exe delete "Zabbix Agent"
sc.exe delete "Zabbix Agent 2"

# 3. Terminar processos
Get-Process | Where-Object { $_.ProcessName -like "*zabbix*" } | Stop-Process -Force

# 4. Remover instalações MSI
Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*Zabbix*" } | ForEach-Object { $_.Uninstall() }

# 5. Remover diretórios
Remove-Item "C:\Program Files\Zabbix Agent*" -Recurse -Force -ErrorAction SilentlyContinue

# 6. Tentar instalação novamente
-ZabbixServerIP "*************" -AgentHostname "SEU-HOSTNAME" -Force
```

### **Solução 3: Instalação Offline**
Se as soluções anteriores não funcionarem:

```powershell
# 1. Baixe manualmente o MSI:
# https://cdn.zabbix.com/zabbix/binaries/stable/7.0/7.0.6/zabbix_agent2-7.0.6-windows-amd64-openssl.msi

# 2. Coloque em C:\temp\

# 3. Execute com caminho local:
-ZabbixServerIP "*************" -AgentHostname "SEU-HOSTNAME" -LocalMSIPath "C:\temp\zabbix_agent2-7.0.6-windows-amd64-openssl.msi" -Force
```

### **Solução 4: Verificações Adicionais**
```powershell
# Verificar se está executando como Administrador
([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

# Verificar Windows Installer Service
Get-Service -Name "msiserver"
Start-Service -Name "msiserver"

# Verificar espaço em disco (mínimo 1GB livre)
Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID, @{Name="FreeGB";Expression={[math]::Round($_.FreeSpace/1GB,2)}}

# Limpar arquivos temporários
Remove-Item "$env:TEMP\*zabbix*" -Force -Recurse -ErrorAction SilentlyContinue
```

## 📋 Checklist de Verificação

Antes de tentar a instalação novamente, verifique:

- [ ] ✅ **Executando como Administrador**
- [ ] ✅ **Nenhum serviço do Zabbix rodando**
- [ ] ✅ **Nenhum processo do Zabbix ativo**
- [ ] ✅ **Nenhuma instalação MSI anterior**
- [ ] ✅ **Diretórios do Zabbix removidos**
- [ ] ✅ **Windows Installer Service rodando**
- [ ] ✅ **Espaço suficiente em disco (>1GB)**
- [ ] ✅ **Arquivos temporários limpos**

## 🎯 Comando Final Recomendado

Após executar as correções:

```powershell
# Para o seu caso específico:
-ZabbixServerIP "*************" -AgentHostname "segundotab-servidor-sistema-ad" -Force
```

## 📊 Logs Úteis para Diagnóstico

```powershell
# Log de instalação MSI (criado automaticamente)
Get-Content "$env:TEMP\zabbix_install.log" -Tail 50

# Verificar eventos do Windows
Get-EventLog -LogName Application -Source "MsiInstaller" -Newest 10

# Log do script (se disponível)
Get-Content "C:\temp\zabbix_script.log" -Tail 20
```

## 🆘 Se Nada Funcionar

1. **Reinicie o computador** e tente novamente
2. **Desabilite temporariamente o antivírus**
3. **Execute em Modo de Segurança** (se possível)
4. **Use instalação manual do MSI**:
   ```cmd
   msiexec /i "caminho\para\zabbix_agent2.msi" /l*v "C:\temp\install.log" SERVER=************* HOSTNAME=segundotab-servidor-sistema-ad
   ```

## 📞 Suporte Adicional

Se o problema persistir:
1. Execute `Fix-MSI-Error-1603.ps1` e envie o resultado
2. Verifique o log em `$env:TEMP\zabbix_install.log`
3. Verifique eventos do Windows relacionados ao MSI

---

**💡 Dica**: O erro 1603 é quase sempre causado por restos de instalações anteriores. A limpeza completa resolve 95% dos casos!
