import requests
from datetime import datetime, timedelta
from config import MOVIEDESK_API_TOKEN, MOVIEDESK_BASE_URL

def buscar_tickets_por_titulo(titulo_exato):
    url = f"{MOVIEDESK_BASE_URL}/tickets"
    todos_tickets = []
    skip = 0
    limite_por_pagina = 1000

    # 🔥 Limita para tickets criados nos últimos 7 dias
    data_limite = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%dT00:00:00")

    while True:
        params = {
            "token": MOVIEDESK_API_TOKEN,
            "filter": f"subject eq '{titulo_exato}' and baseStatus in ('New','InAttendance','Waiting') and createdDate ge {data_limite}",
            "$select": "id,subject,status,baseStatus,createdDate",
            "$expand": "actions",
            "$top": limite_por_pagina,
            "$skip": skip
        }

        response = requests.get(url, params=params)

        if not response.ok:
            print(f"❌ Erro ao buscar tickets: {response.status_code} - {response.text}")
            break

        page_tickets = response.json()
        todos_tickets.extend(page_tickets)

        if len(page_tickets) < limite_por_pagina:
            break

        skip += limite_por_pagina

    return todos_tickets


def responder_ticket(ticket_id, resposta):
    url = f"{MOVIEDESK_BASE_URL}/tickets/{ticket_id}/actions?token={MOVIEDESK_API_TOKEN}"
    payload = {
        "type": 1,
        "description": resposta,
        "origin": 2,  # Origem: Web API
        "status": 1   # Mantém como aberto
    }
    response = requests.post(url, json=payload)
    return response.status_code
