#!/bin/bash

# Script para preparar imagem visual do Raspberry Pi com instalação automática
# Este script deve ser executado em um Raspberry Pi limpo para criar a imagem base
# Autor: <PERSON> Matheus
# Data: $(date +%Y-%m-%d)

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

log "=== PREPARADOR DE IMAGEM VISUAL RASPBERRY PI PLUG AND PLAY ==="
log "Configurando Raspberry Pi para instalação automática VISUAL no primeiro boot"
log "Desenvolvido por Paulo Matheus - NVirtual"

# Verificar se está rodando como root
if [[ $EUID -ne 0 ]]; then
   error "Este script deve ser executado como root (sudo)"
fi

# Verificar se está em um Raspberry Pi
if ! grep -q "Raspberry Pi" /proc/cpuinfo 2>/dev/null; then
    warning "⚠️ Este script foi desenvolvido para Raspberry Pi"
    read -p "Deseja continuar mesmo assim? (s/N): " CONTINUE
    if [[ ! "$CONTINUE" =~ ^[Ss]$ ]]; then
        exit 0
    fi
fi

# Verificar arquivos necessários
log "Verificando arquivos necessários..."
REQUIRED_FILES=("setup_first_boot_visual.sh" "install_visual_boot_service.sh")
for file in "${REQUIRED_FILES[@]}"; do
    if [[ ! -f "$file" ]]; then
        error "Arquivo necessário não encontrado: $file"
    fi
done

log "✅ Todos os arquivos necessários encontrados"

# Atualizar sistema base
log "Atualizando sistema base..."
apt-get update -y
apt-get upgrade -y

# Instalar dependências básicas
log "Instalando dependências básicas..."
apt-get install -y curl wget git vim nano htop

# Configurar timezone para Brasil
log "Configurando timezone..."
timedatectl set-timezone America/Sao_Paulo

# Habilitar SSH se não estiver habilitado
log "Verificando SSH..."
if ! systemctl is-enabled ssh > /dev/null 2>&1; then
    log "Habilitando SSH..."
    systemctl enable ssh
    systemctl start ssh
else
    log "✅ SSH já está habilitado"
fi

# Executar o instalador do serviço de primeiro boot visual
log "Configurando serviço de primeiro boot visual..."
chmod +x install_visual_boot_service.sh
./install_visual_boot_service.sh

# Configurar hostname padrão
log "Configurando hostname padrão..."
echo "raspberrypi-zabbix" > /etc/hostname
sed -i 's/*********.*/*********\traspberrypi-zabbix/' /etc/hosts

# Configurar splash screen personalizado
log "Configurando splash screen..."
cat > /usr/share/plymouth/themes/pix/splash.png << 'EOF'
# Aqui você pode adicionar uma imagem personalizada se desejar
# Por enquanto, vamos usar a configuração padrão
EOF

# Configurar mensagem de boot personalizada
cat > /etc/issue << 'EOF'

 ____                 _                          ____  _ 
|  _ \ __ _ ___ _ __   | |__   ___ _ __ _ __ _   _ |  _ \(_)
| |_) / _` / __| '_ \  | '_ \ / _ \ '__| '__| | | || |_) | |
|  _ < (_| \__ \ |_) | | |_) |  __/ |  | |  | |_| ||  __/| |
|_| \_\__,_|___/ .__/  |_.__/ \___|_|  |_|   \__, ||_|   |_|
               |_|                          |___/          

=== RASPBERRY PI PLUG AND PLAY (VISUAL) ===
🖥️  Instalação automática com exibição visual
⏱️  Aguarde 10-15 minutos para configuração completa
🌐 Certifique-se de ter conexão com internet

Desenvolvido por Paulo Matheus - NVirtual

EOF

# Criar arquivo de identificação da imagem visual
log "Criando arquivo de identificação..."
cat > /home/<USER>/IMAGEM_VISUAL_INFO.txt << EOF
=== RASPBERRY PI PLUG AND PLAY VISUAL - INFORMAÇÕES DA IMAGEM ===

Data de criação: $(date)
Versão do sistema: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)
Desenvolvido por: Paulo Matheus - NVirtual

=== CARACTERÍSTICAS DESTA IMAGEM VISUAL ===
✅ Sistema base atualizado
✅ SSH habilitado
✅ Timezone configurado (America/Sao_Paulo)
✅ Serviço de primeiro boot VISUAL configurado
✅ Dependências básicas instaladas
✅ Exibição em tempo real na tela

=== INSTALAÇÃO VISUAL NO PRIMEIRO BOOT ===
🖥️  Exibição na tela principal (tty1)
📊 Barras de progresso coloridas
⏱️  Status detalhado de cada etapa
🎯 Tela de finalização com resumo

=== COMPONENTES INSTALADOS AUTOMATICAMENTE ===
- Zabbix Proxy (hostname: "MUDAR")
- Zabbix Agent
- Tactical RMM (Client ID: 1, Site ID: 1)
- Firewall básico
- Configuração de rede

=== REQUISITOS PARA USO ===
1. Conexão com internet no primeiro boot
2. Monitor conectado para ver o progresso
3. Aguardar 10-15 minutos para instalação completa
4. Verificar arquivo /home/<USER>/SISTEMA_INFO.txt após instalação

=== ETAPAS VISUAIS QUE SERÃO EXIBIDAS ===
1. 🌐 Verificação de conectividade
2. 📥 Download do script
3. ⚙️  Configuração automática
4. 🔧 Instalação Zabbix
5. 📡 Instalação Tactical RMM
6. 🔥 Configuração firewall
7. 🌍 Configuração de rede
8. ✅ Finalização

=== PRÓXIMOS PASSOS APÓS PRIMEIRO BOOT ===
1. Aguardar exibição da tela de sucesso
2. Sistema reiniciará automaticamente
3. Acessar Zabbix Server: monitora.nvirtual.com.br
4. Alterar nome do proxy de "MUDAR" para nome desejado
5. Sistema estará automaticamente no Tactical RMM

=== COMANDOS ÚTEIS ===
check-visual                   # Verificar status da instalação visual
sudo journalctl -u first-boot-setup-visual.service -f  # Ver logs detalhados

=== DIFERENÇAS DA VERSÃO VISUAL ===
• Exibição em tempo real na tela
• Barras de progresso coloridas
• Interface mais amigável
• Status detalhado de cada etapa
• Tela de finalização informativa

=== SUPORTE ===
Desenvolvido por: Paulo Matheus - NVirtual
EOF

# Definir proprietário correto baseado no usuário existente
if [[ -d "/home/<USER>" ]] && id "suportenv" &>/dev/null; then
    chown suportenv:suportenv /home/<USER>/IMAGEM_VISUAL_INFO.txt 2>/dev/null || true
else
    chmod 644 /home/<USER>/IMAGEM_VISUAL_INFO.txt
fi

# Configurar mensagem de boas-vindas visual
log "Configurando mensagem de boas-vindas visual..."
cat > /etc/motd << 'EOF'

 ____                 _                          ____  _ 
|  _ \ __ _ ___ _ __   | |__   ___ _ __ _ __ _   _ |  _ \(_)
| |_) / _` / __| '_ \  | '_ \ / _ \ '__| '__| | | || |_) | |
|  _ < (_| \__ \ |_) | | |_) |  __/ |  | |  | |_| ||  __/| |
|_| \_\__,_|___/ .__/  |_.__/ \___|_|  |_|   \__, ||_|   |_|
               |_|                          |___/          

=== RASPBERRY PI PLUG AND PLAY VISUAL ===
Desenvolvido por Paulo Matheus - NVirtual

🖥️  INSTALAÇÃO AUTOMÁTICA VISUAL NO PRIMEIRO BOOT
   - Exibição em tempo real na tela
   - Barras de progresso coloridas
   - Status detalhado de cada etapa

📋 VERIFICAR STATUS: check-visual
📄 INFORMAÇÕES: cat /home/<USER>/IMAGEM_VISUAL_INFO.txt

⚠️  IMPORTANTE: 
   • Conecte um monitor para ver o progresso
   • Certifique-se de ter conexão com internet
   • Aguarde 10-15 minutos para instalação completa

EOF

# Limpar histórico e logs para imagem limpa
log "Limpando sistema para imagem..."
history -c
> /home/<USER>/.bash_history
journalctl --vacuum-time=1d
apt-get autoremove -y
apt-get autoclean

# Remover arquivos de instalação
log "Removendo arquivos de instalação..."
rm -f install_visual_boot_service.sh

log "✅ Preparação da imagem visual concluída com sucesso!"
echo
echo "=========================================="
echo "IMAGEM VISUAL RASPBERRY PI PLUG AND PLAY PRONTA"
echo "=========================================="
echo
info "🎯 A imagem visual está pronta para ser clonada!"
echo
info "📋 O que foi configurado:"
info "• Sistema base atualizado"
info "• SSH habilitado"
info "• Timezone Brasil configurado"
info "• Serviço de primeiro boot VISUAL instalado"
info "• Exibição em tempo real configurada"
info "• Mensagens informativas criadas"
echo
warning "⚠️  PRÓXIMOS PASSOS:"
warning "1. Desligue o Raspberry Pi: sudo shutdown -h now"
warning "2. Clone o SD card para criar a imagem base"
warning "3. Use esta imagem para novos deployments"
echo
info "🔧 COMO USAR A IMAGEM VISUAL:"
info "1. Grave a imagem em um novo SD card"
info "2. Insira no Raspberry Pi"
info "3. Conecte monitor e cabo de rede"
info "4. Ligue o sistema"
info "5. Acompanhe a instalação na tela"
info "6. Aguarde tela de sucesso e reinicialização"
info "7. Acesse monitora.nvirtual.com.br e altere nome do proxy"
echo
log "🎉 Imagem visual plug-and-play criada com sucesso!"
