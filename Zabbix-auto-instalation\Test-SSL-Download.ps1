# Script de teste para verificar conectividade SSL/TLS
# Use este script para testar se o problema de SSL foi resolvido
# Autor: <PERSON> - NVirtual

param(
    [Parameter(Mandatory=$false)]
    [string]$TestUrl = "https://cdn.zabbix.com/zabbix/binaries/stable/7.0/7.0.6/zabbix_agent2-7.0.6-windows-amd64-openssl.msi"
)

# Função para log
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch($Level) {
        "ERROR" { "Red" }
        "WARNING" { "Yellow" }
        "SUCCESS" { "Green" }
        default { "White" }
    }
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

Write-Log "=== TESTE DE CONECTIVIDADE SSL/TLS ===" -Level "SUCCESS"
Write-Log "URL de teste: $TestUrl"

# 1. Verificar versão do PowerShell
$psVersion = $PSVersionTable.PSVersion
Write-Log "PowerShell versão: $($psVersion.Major).$($psVersion.Minor)"

# 2. Verificar versão do .NET Framework
$netVersion = [System.Environment]::Version
Write-Log ".NET Framework versão: $netVersion"

# 3. Verificar protocolos SSL/TLS disponíveis
$availableProtocols = [Enum]::GetNames([Net.SecurityProtocolType])
Write-Log "Protocolos SSL/TLS disponíveis: $($availableProtocols -join ', ')"

# 4. Configurar protocolos SSL/TLS
Write-Log "Configurando protocolos SSL/TLS..."
try {
    # Protocolo atual antes da configuração
    Write-Log "Protocolo atual (antes): $([Net.ServicePointManager]::SecurityProtocol)"
    
    # Configurar TLS 1.2, 1.1 e 1.0
    if ([Net.SecurityProtocolType].GetEnumNames() -contains "Tls12") {
        [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12 -bor [Net.SecurityProtocolType]::Tls11 -bor [Net.SecurityProtocolType]::Tls
        Write-Log "TLS 1.2, 1.1 e 1.0 configurados" -Level "SUCCESS"
    } else {
        [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls11 -bor [Net.SecurityProtocolType]::Tls
        Write-Log "TLS 1.1 e 1.0 configurados (TLS 1.2 não disponível)" -Level "WARNING"
    }
    
    # Protocolo atual após a configuração
    Write-Log "Protocolo atual (depois): $([Net.ServicePointManager]::SecurityProtocol)"
}
catch {
    Write-Log "Erro ao configurar SSL/TLS: $($_.Exception.Message)" -Level "ERROR"
}

# 5. Testar conectividade básica
Write-Log "Testando conectividade básica..."
$uri = [System.Uri]$TestUrl
$hostname = $uri.Host
$port = if ($uri.Port -eq -1) { 443 } else { $uri.Port }

try {
    $tcpTest = Test-NetConnection -ComputerName $hostname -Port $port -WarningAction SilentlyContinue
    if ($tcpTest.TcpTestSucceeded) {
        Write-Log "Conectividade TCP para $hostname`:$port : OK" -Level "SUCCESS"
    } else {
        Write-Log "Conectividade TCP para $hostname`:$port : FALHOU" -Level "ERROR"
    }
}
catch {
    Write-Log "Erro no teste TCP: $($_.Exception.Message)" -Level "WARNING"
}

# 6. Testar download com Invoke-WebRequest
Write-Log "Testando download com Invoke-WebRequest..."
$testPath = Join-Path $env:TEMP "zabbix_test_download.msi"

try {
    # Remover arquivo de teste se existir
    if (Test-Path $testPath) {
        Remove-Item $testPath -Force
    }
    
    # Testar apenas os primeiros bytes (Range request)
    $headers = @{
        'Range' = 'bytes=0-1023'
        'User-Agent' = 'PowerShell-ZabbixInstaller-Test/1.0'
    }
    
    $response = Invoke-WebRequest -Uri $TestUrl -Headers $headers -UseBasicParsing -TimeoutSec 30
    Write-Log "Invoke-WebRequest: OK (Status: $($response.StatusCode), Bytes: $($response.Content.Length))" -Level "SUCCESS"
}
catch {
    Write-Log "Invoke-WebRequest falhou: $($_.Exception.Message)" -Level "ERROR"
    
    # 7. Testar download com WebClient
    Write-Log "Testando download com WebClient..."
    try {
        $webClient = New-Object System.Net.WebClient
        $webClient.Headers.Add("User-Agent", "PowerShell-ZabbixInstaller-Test/1.0")
        
        # Ignorar erros de certificado temporariamente para teste
        [System.Net.ServicePointManager]::ServerCertificateValidationCallback = {$true}
        
        # Testar apenas download de uma pequena parte
        $testData = $webClient.DownloadData($TestUrl)
        Write-Log "WebClient: OK (Bytes baixados: $($testData.Length))" -Level "SUCCESS"
        
        # Restaurar validação de certificado
        [System.Net.ServicePointManager]::ServerCertificateValidationCallback = $null
    }
    catch {
        Write-Log "WebClient também falhou: $($_.Exception.Message)" -Level "ERROR"
    }
}

# 8. Testar URLs alternativas
Write-Log "Testando URLs alternativas..."
$alternativeUrls = @(
    "https://repo.zabbix.com/zabbix/7.0/windows/zabbix_agent2-7.0.6-windows-amd64-openssl.msi",
    "https://sourceforge.net/projects/zabbix/files/ZABBIX%20Latest%20Stable/7.0.6/zabbix_agent2-7.0.6-windows-amd64-openssl.msi/download"
)

foreach ($altUrl in $alternativeUrls) {
    try {
        Write-Log "Testando: $altUrl"
        $headers = @{
            'Range' = 'bytes=0-1023'
            'User-Agent' = 'PowerShell-ZabbixInstaller-Test/1.0'
        }
        $response = Invoke-WebRequest -Uri $altUrl -Headers $headers -UseBasicParsing -TimeoutSec 30
        Write-Log "URL alternativa OK: $altUrl (Status: $($response.StatusCode))" -Level "SUCCESS"
        break
    }
    catch {
        Write-Log "URL alternativa falhou: $altUrl - $($_.Exception.Message)" -Level "WARNING"
    }
}

# 9. Verificar configurações de proxy
Write-Log "Verificando configurações de proxy..."
try {
    $proxySettings = [System.Net.WebRequest]::GetSystemWebProxy()
    $proxyUri = $proxySettings.GetProxy([System.Uri]$TestUrl)
    
    if ($proxyUri.ToString() -eq $TestUrl) {
        Write-Log "Nenhum proxy detectado" -Level "INFO"
    } else {
        Write-Log "Proxy detectado: $($proxyUri.ToString())" -Level "WARNING"
        Write-Log "Se houver problemas, configure o proxy no script principal" -Level "INFO"
    }
}
catch {
    Write-Log "Erro ao verificar proxy: $($_.Exception.Message)" -Level "WARNING"
}

# 10. Resumo e recomendações
Write-Log "=== RESUMO DO TESTE ===" -Level "SUCCESS"

if ($tcpTest.TcpTestSucceeded) {
    Write-Log "✅ Conectividade de rede: OK" -Level "SUCCESS"
} else {
    Write-Log "❌ Conectividade de rede: PROBLEMA" -Level "ERROR"
}

Write-Log "=== RECOMENDAÇÕES ===" -Level "INFO"
Write-Log "1. Se o teste passou, execute o script principal novamente"
Write-Log "2. Se ainda houver problemas, verifique:"
Write-Log "   - Configurações de firewall corporativo"
Write-Log "   - Configurações de proxy"
Write-Log "   - Políticas de segurança do Windows"
Write-Log "3. Como alternativa, baixe o MSI manualmente e coloque em C:\temp\"

# Limpeza
if (Test-Path $testPath) {
    Remove-Item $testPath -Force -ErrorAction SilentlyContinue
}

Write-Log "Teste concluído!" -Level "SUCCESS"
