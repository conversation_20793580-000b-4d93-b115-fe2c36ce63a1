#!/bin/bash

# Script de demonstração para mostrar como os scripts aparecem em tela
# Este script simula a execução dos scripts de instalação para demonstração
# Autor: <PERSON> Matheus - NVirtual

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# Função para limpar tela e mostrar header
show_header() {
    clear > /dev/tty1 2>/dev/null || true
    local header="
╔══════════════════════════════════════════════════════════════╗
║                DEMONSTRAÇÃO VISUAL                          ║
║            Scripts Aparecendo em Tela                       ║
║                                                              ║
║  🔧 Simulando execução dos scripts                          ║
║  📺 Mostrando output em tempo real                          ║
║                                                              ║
║  Desenvolvido por: Paulo Matheus - NVirtual                ║
╚══════════════════════════════════════════════════════════════╝
"
    echo -e "${GREEN}$header${NC}" > /dev/tty1 2>/dev/null || true
    echo -e "${GREEN}$header${NC}"
}

# Função para log com exibição na tela
log() {
    local message="✅ [$(date '+%H:%M:%S')] $1"
    echo -e "${GREEN}$message${NC}"
    echo -e "${GREEN}$message${NC}" > /dev/tty1 2>/dev/null || true
}

info() {
    local message="ℹ️  [INFO] $1"
    echo -e "${BLUE}$message${NC}"
    echo -e "${BLUE}$message${NC}" > /dev/tty1 2>/dev/null || true
}

warning() {
    local message="⚠️  [AVISO] $1"
    echo -e "${YELLOW}$message${NC}"
    echo -e "${YELLOW}$message${NC}" > /dev/tty1 2>/dev/null || true
}

# Função para mostrar status atual
show_status() {
    local status="$1"
    local details="$2"
    local status_msg="
┌─────────────────────────────────────────────────────────────┐
│ STATUS: $status
│ $details
└─────────────────────────────────────────────────────────────┘"
    
    echo -e "${CYAN}$status_msg${NC}" > /dev/tty1 2>/dev/null || true
    echo -e "${CYAN}$status_msg${NC}"
}

# Função para simular execução de script com output visível
simulate_script_execution() {
    local script_name="$1"
    local steps=("$@")
    steps=("${steps[@]:1}")  # Remove primeiro elemento (nome do script)
    
    log "🚀 Executando: $script_name"
    show_status "EXECUTANDO" "Script: $script_name"
    
    for step in "${steps[@]}"; do
        echo -e "${BLUE}[SCRIPT] $step${NC}" > /dev/tty1 2>/dev/null || true
        echo -e "${BLUE}[SCRIPT] $step${NC}"
        sleep 2
    done
    
    log "✅ Script $script_name concluído!"
}

# Mostrar header
show_header

log "=== DEMONSTRAÇÃO DE EXECUÇÃO VISUAL ==="
log "Este script demonstra como os outros scripts aparecem em tela"

sleep 3

# Simular bootstrap
log "📥 Simulando bootstrap_first_boot.sh"
show_status "BOOTSTRAP" "Baixando e executando configuração automática..."

simulate_script_execution "setup_first_boot.sh" \
    "✅ Verificando conectividade de rede..." \
    "✅ Baixando script de instalação do GitHub..." \
    "✅ Preparando instalação automática..." \
    "✅ Modificando script para execução não-interativa..." \
    "✅ Iniciando instalação do Zabbix e Tactical RMM..."

sleep 2

# Simular script principal
log "🔧 Simulando install_zabbix_tactical_rmm.sh"
show_status "INSTALAÇÃO" "Executando script principal de instalação..."

simulate_script_execution "install_zabbix_tactical_rmm.sh" \
    "✅ Atualizando repositórios do sistema..." \
    "✅ Instalando dependências necessárias..." \
    "✅ Configurando repositório Zabbix 7.0..." \
    "✅ Instalando Zabbix Proxy com SQLite3..." \
    "✅ Instalando Zabbix Agent..." \
    "✅ Configurando arquivo zabbix_proxy.conf..." \
    "✅ Configurando arquivo zabbix_agentd.conf..." \
    "✅ Habilitando serviços Zabbix..." \
    "✅ Iniciando Zabbix Proxy..." \
    "✅ Iniciando Zabbix Agent..." \
    "✅ Baixando script do Tactical RMM..." \
    "✅ Instalando Tactical RMM Agent..." \
    "✅ Configurando MeshAgent..." \
    "✅ Iniciando serviços Tactical RMM..." \
    "✅ Configurando firewall básico..." \
    "✅ Aplicando regras de firewall..."

sleep 2

# Simular verificação final
log "🔍 Simulando verificação de serviços"
show_status "VERIFICAÇÃO" "Testando serviços instalados..."

simulate_script_execution "verificacao_servicos" \
    "✅ Zabbix Proxy está ativo e funcionando" \
    "✅ Zabbix Agent está ativo e funcionando" \
    "✅ Tactical RMM Agent está ativo" \
    "✅ MeshAgent está rodando" \
    "✅ Firewall configurado corretamente" \
    "✅ Conectividade com servidores confirmada"

sleep 2

# Mostrar tela final
show_final_demo() {
    clear > /dev/tty1 2>/dev/null || true
    local final_screen="
╔══════════════════════════════════════════════════════════════╗
║                    ✅ DEMONSTRAÇÃO CONCLUÍDA!               ║
╠══════════════════════════════════════════════════════════════╣
║                                                              ║
║  🎉 Agora você viu como os scripts aparecem em tela!        ║
║                                                              ║
║  📋 O QUE O TÉCNICO VERÁ:                                   ║
║  • Todas as etapas de instalação em tempo real              ║
║  • Status de cada comando executado                         ║
║  • Mensagens de sucesso/erro/aviso                          ║
║  • Progresso visual da instalação                           ║
║                                                              ║
║  🔧 SCRIPTS MODIFICADOS:                                    ║
║  • bootstrap_first_boot.sh - Output em tempo real           ║
║  • setup_first_boot.sh - Execução com tee                   ║
║  • setup_first_boot_visual.sh - Progresso visual            ║
║                                                              ║
║  📺 BENEFÍCIOS PARA O TÉCNICO:                              ║
║  • Acompanhar progresso da instalação                       ║
║  • Identificar problemas rapidamente                        ║
║  • Ver confirmação de cada etapa                            ║
║  • Não ficar no escuro durante a instalação                 ║
║                                                              ║
║  Desenvolvido por: Paulo Matheus - NVirtual                ║
╚══════════════════════════════════════════════════════════════╝
"
    echo -e "${GREEN}$final_screen${NC}" > /dev/tty1 2>/dev/null || true
    echo -e "${GREEN}$final_screen${NC}"
}

show_final_demo

log "🎯 Demonstração concluída!"
log "📝 Os scripts agora mostram todo o progresso em tela para o técnico"
log "🔧 Use os scripts modificados para instalação real"

echo ""
info "Scripts disponíveis com output visual:"
info "• bootstrap_first_boot.sh - Bootstrap com progresso"
info "• setup_first_boot.sh - Instalação com output completo"
info "• setup_first_boot_visual.sh - Instalação com interface visual"
echo ""

read -p "Pressione ENTER para finalizar a demonstração..."
