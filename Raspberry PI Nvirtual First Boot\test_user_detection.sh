#!/bin/bash

# Script de teste para verificar detecção de usuário e criação de diretórios
# Autor: <PERSON>
# Data: $(date +%Y-%m-%d)

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[TEST] $1${NC}"
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

echo "=========================================="
echo "TESTE DE DETECÇÃO DE USUÁRIO E DIRETÓRIOS"
echo "=========================================="
echo

log "Testando lógica de detecção de diretório do usuário..."

# Simular a lógica do script corrigido
USER_HOME="/home/<USER>"
if [[ ! -d "/home/<USER>" ]]; then
    warning "Diretório /home/<USER>"

    # Tentar outros usuários comuns
    if [[ -d "/home/<USER>" ]]; then
        USER_HOME="/home/<USER>"
        info "Usando diretório: $USER_HOME"
    elif [[ -d "/home/<USER>" ]]; then
        USER_HOME="/home/<USER>"
        info "Usando diretório: $USER_HOME"
    else
        warning "Nenhum diretório de usuário padrão encontrado"
        info "Seria criado: /home/<USER>"
        USER_HOME="/home/<USER>"
    fi
else
    log "Diretório /home/<USER>"
fi

echo
info "Diretório selecionado: $USER_HOME"

# Verificar usuário atual
echo
log "Informações do sistema atual:"
info "Usuário atual: $(whoami)"
info "Diretório home atual: $HOME"
info "UID atual: $(id -u)"

# Verificar usuários existentes
echo
log "Usuários existentes no sistema:"
for user in suportenv ubuntu debian; do
    if id "$user" &>/dev/null; then
        log "✅ Usuário '$user' existe"
        info "   Home: $(eval echo ~$user)"
        info "   UID: $(id -u $user)"
        # Verificar se está no grupo sudo
        if groups "$user" | grep -q sudo; then
            info "   Grupos: $(groups $user | cut -d: -f2)"
        fi
    else
        warning "❌ Usuário '$user' não existe"
    fi
done

# Testar criação de arquivo de teste
echo
log "Testando criação de arquivo de informações..."

TEST_FILE="$USER_HOME/TESTE_SISTEMA_INFO.txt"
TEST_DIR=$(dirname "$TEST_FILE")

if [[ ! -d "$TEST_DIR" ]]; then
    warning "Diretório $TEST_DIR não existe"
    if [[ $(id -u) -eq 0 ]]; then
        info "Executando como root - criando diretório..."
        mkdir -p "$TEST_DIR"
        log "✅ Diretório criado: $TEST_DIR"
    else
        error "Não é possível criar diretório sem privilégios de root"
        exit 1
    fi
else
    log "✅ Diretório existe: $TEST_DIR"
fi

# Criar arquivo de teste
cat > "$TEST_FILE" << EOF
=== ARQUIVO DE TESTE ===
Data: $(date)
Usuário que executou: $(whoami)
Diretório usado: $USER_HOME
Sistema: $(uname -a)
EOF

if [[ -f "$TEST_FILE" ]]; then
    log "✅ Arquivo de teste criado: $TEST_FILE"
    
    # Definir proprietário correto
    USER_NAME=$(basename "$USER_HOME")
    if id "$USER_NAME" &>/dev/null; then
        if [[ $(id -u) -eq 0 ]]; then
            chown "$USER_NAME:$USER_NAME" "$TEST_FILE"
            log "✅ Proprietário definido: $USER_NAME"
        else
            warning "Não é root - não é possível alterar proprietário"
        fi
    else
        warning "Usuário '$USER_NAME' não existe - mantendo como $(whoami)"
        chmod 644 "$TEST_FILE"
        log "✅ Permissões definidas: 644"
    fi
    
    # Mostrar informações do arquivo
    echo
    info "Informações do arquivo criado:"
    ls -la "$TEST_FILE"
    
    echo
    info "Conteúdo do arquivo:"
    cat "$TEST_FILE"
    
    # Limpar arquivo de teste
    rm -f "$TEST_FILE"
    log "🗑️ Arquivo de teste removido"
    
else
    error "❌ Falha ao criar arquivo de teste"
fi

# Testar arquivo de flag
echo
log "Testando arquivo de flag..."

FLAG_FILE="/var/lib/first_boot_completed"
FLAG_DIR=$(dirname "$FLAG_FILE")

if [[ ! -d "$FLAG_DIR" ]]; then
    if [[ $(id -u) -eq 0 ]]; then
        mkdir -p "$FLAG_DIR"
        log "✅ Diretório criado: $FLAG_DIR"
    else
        warning "Não é possível criar $FLAG_DIR sem root"
    fi
fi

if [[ -w "$FLAG_DIR" ]] || [[ $(id -u) -eq 0 ]]; then
    echo "$(date): Teste de flag concluído" > "$FLAG_FILE"
    chmod 644 "$FLAG_FILE"
    log "✅ Arquivo de flag criado: $FLAG_FILE"
    
    # Mostrar informações
    ls -la "$FLAG_FILE"
    
    # Limpar
    rm -f "$FLAG_FILE"
    log "🗑️ Arquivo de flag removido"
else
    warning "Não é possível escrever em $FLAG_DIR"
fi

echo
echo "=========================================="
echo "RESUMO DO TESTE"
echo "=========================================="
echo

if [[ -d "/home/<USER>" ]]; then
    log "✅ Sistema com usuário 'suportenv' (NVirtual padrão)"
elif [[ -d "/home/<USER>" ]]; then
    log "✅ Sistema Ubuntu detectado"
elif [[ -d "/home/<USER>" ]]; then
    log "✅ Sistema Debian detectado"
else
    warning "⚠️ Sistema sem usuários padrão - criação necessária"
fi

if [[ $(id -u) -eq 0 ]]; then
    log "✅ Executando como root - todas as operações possíveis"
else
    warning "⚠️ Não executando como root - algumas operações limitadas"
fi

echo
info "O script corrigido deve funcionar corretamente neste sistema!"
